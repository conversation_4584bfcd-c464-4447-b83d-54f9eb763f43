package com.emc.service.impl;

import com.emc.constants.EmcConstants;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class ReferenceServiceImplTest {

    @Test
    void testLoadReference() {
        // Setup
        ReferenceServiceImpl referenceService = new ReferenceServiceImpl();
        
        // Execute
        int result = referenceService.loadReference();
        
        // Verify
        assertEquals(EmcConstants.OK, result);
    }
}
