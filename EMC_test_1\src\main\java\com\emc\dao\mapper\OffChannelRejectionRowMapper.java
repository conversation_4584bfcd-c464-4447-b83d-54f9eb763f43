package com.emc.dao.mapper;

import com.emc.model.OffChannelRejection;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for OffChannelRejection entity.
 */
public class OffChannelRejectionRowMapper implements RowMapper<OffChannelRejection> {
    
    @Override
    public OffChannelRejection mapRow(ResultSet rs, int rowNum) throws SQLException {
        OffChannelRejection offChannelRejection = new OffChannelRejection();
        offChannelRejection.setChannelSeparation(rs.getFloat("CHANNEL_SEPARATION"));
        offChannelRejection.setLowVhfRejection(rs.getFloat("LOW_VHF_REJECTION"));
        offChannelRejection.setVhfRejection(rs.getFloat("VHF_REJECTION"));
        offChannelRejection.setUhfRejection(rs.getFloat("UHF_REJECTION"));
        offChannelRejection.setUhf800Rejection(rs.getFloat("UHF_800_REJECTION"));
        return offChannelRejection;
    }
}
