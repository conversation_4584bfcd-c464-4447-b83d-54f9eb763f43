package com.emc.model;

import lombok.Data;

/**
 * Represents a request for EMC analysis.
 * This is used as input for the REST API.
 */
@Data
public class EmcAnalysisRequest {
    private String username;
    private String password;
    private String printerId;
    private String printFile;

    /**
     * The EMC UID to use for the analysis.
     * For interactive mode, if not provided, defaults to "ELSO_WEB.ANALYSIS".
     */
    private String emcUid;

    private boolean interactive;
    private String interactiveMode; // "-l" or "-s"
    private String batchContent;    // Content of the batch file

    /**
     * Default constructor that initializes default values.
     */
    public EmcAnalysisRequest() {
        // Default values
        this.printFile = "emc_print";
        this.interactive = false;
        this.interactiveMode = "-l";
    }
}
