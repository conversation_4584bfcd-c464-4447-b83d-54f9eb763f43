# Server configuration
server.port=8080

# H2 Database configuration
spring.datasource.url=jdbc:h2:mem:emcdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Application specific properties
emc.directory=${user.dir}/emc_data
emc.batch.directory=${emc.directory}/batch
emc.audit.directory=${emc.directory}/audit
emc.summary.directory=${emc.directory}/summary
emc.interactive.directory=${emc.directory}/interactive
