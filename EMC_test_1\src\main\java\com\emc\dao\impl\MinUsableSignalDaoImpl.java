package com.emc.dao.impl;

import com.emc.dao.MinUsableSignalDao;
import com.emc.dao.mapper.MinUsableSignalRowMapper;
import com.emc.model.MinUsableSignal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * JDBC implementation of MinUsableSignalDao.
 */
@Repository
@Slf4j
public class MinUsableSignalDaoImpl implements MinUsableSignalDao {
    
    private final JdbcTemplate jdbcTemplate;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final MinUsableSignalRowMapper rowMapper;
    
    // SQL queries
    private static final String SELECT_ALL = 
        "SELECT ID, LOW_VHF_SIGNAL, VHF_SIGNAL, UHF_SIGNAL, UHF_800_SIGNAL FROM MIN_SIGNAL_TAB";
    
    private static final String SELECT_BY_ID = SELECT_ALL + " WHERE ID = ?";
    private static final String SELECT_CONFIG = SELECT_ALL + " LIMIT 1";
    
    private static final String INSERT = 
        "INSERT INTO MIN_SIGNAL_TAB (LOW_VHF_SIGNAL, VHF_SIGNAL, UHF_SIGNAL, UHF_800_SIGNAL) " +
        "VALUES (?, ?, ?, ?)";
    
    private static final String UPDATE = 
        "UPDATE MIN_SIGNAL_TAB SET LOW_VHF_SIGNAL = ?, VHF_SIGNAL = ?, UHF_SIGNAL = ?, UHF_800_SIGNAL = ? " +
        "WHERE ID = ?";
    
    private static final String UPDATE_CONFIG = 
        "UPDATE MIN_SIGNAL_TAB SET LOW_VHF_SIGNAL = ?, VHF_SIGNAL = ?, UHF_SIGNAL = ?, UHF_800_SIGNAL = ?";
    
    private static final String DELETE = "DELETE FROM MIN_SIGNAL_TAB WHERE ID = ?";
    private static final String EXISTS = "SELECT COUNT(*) FROM MIN_SIGNAL_TAB WHERE ID = ?";
    private static final String COUNT = "SELECT COUNT(*) FROM MIN_SIGNAL_TAB";
    
    @Autowired
    public MinUsableSignalDaoImpl(JdbcTemplate jdbcTemplate, NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.rowMapper = new MinUsableSignalRowMapper();
    }
    
    @Override
    public MinUsableSignal save(MinUsableSignal entity) {
        log.debug("Saving MinUsableSignal");
        
        jdbcTemplate.update(INSERT,
            entity.getLowVhfSignal(),
            entity.getVhfSignal(),
            entity.getUhfSignal(),
            entity.getUhf800Signal()
        );
        
        return entity;
    }
    
    @Override
    public MinUsableSignal update(MinUsableSignal entity) {
        log.debug("Updating MinUsableSignal");
        
        // For configuration tables, we typically update all records
        int rowsAffected = jdbcTemplate.update(UPDATE_CONFIG,
            entity.getLowVhfSignal(),
            entity.getVhfSignal(),
            entity.getUhfSignal(),
            entity.getUhf800Signal()
        );
        
        if (rowsAffected == 0) {
            // If no records exist, insert a new one
            return save(entity);
        }
        
        return entity;
    }
    
    @Override
    public Optional<MinUsableSignal> findById(Long id) {
        log.debug("Finding MinUsableSignal by ID: {}", id);
        try {
            MinUsableSignal signal = jdbcTemplate.queryForObject(SELECT_BY_ID, rowMapper, id);
            return Optional.ofNullable(signal);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    @Override
    public List<MinUsableSignal> findAll() {
        log.debug("Finding all MinUsableSignals");
        return jdbcTemplate.query(SELECT_ALL, rowMapper);
    }
    
    @Override
    public boolean deleteById(Long id) {
        log.debug("Deleting MinUsableSignal: {}", id);
        int rowsAffected = jdbcTemplate.update(DELETE, id);
        return rowsAffected > 0;
    }
    
    @Override
    public boolean existsById(Long id) {
        Integer count = jdbcTemplate.queryForObject(EXISTS, Integer.class, id);
        return count != null && count > 0;
    }
    
    @Override
    public long count() {
        Integer count = jdbcTemplate.queryForObject(COUNT, Integer.class);
        return count != null ? count : 0;
    }
    
    @Override
    public MinUsableSignal getConfiguration() {
        log.debug("Getting MinUsableSignal configuration");
        try {
            return jdbcTemplate.queryForObject(SELECT_CONFIG, rowMapper);
        } catch (EmptyResultDataAccessException e) {
            log.warn("No MinUsableSignal configuration found, returning default values");
            // Return default configuration
            MinUsableSignal defaultConfig = new MinUsableSignal();
            defaultConfig.setLowVhfSignal(-120.0f);
            defaultConfig.setVhfSignal(-120.0f);
            defaultConfig.setUhfSignal(-120.0f);
            defaultConfig.setUhf800Signal(-120.0f);
            return defaultConfig;
        }
    }
    
    @Override
    public MinUsableSignal updateConfiguration(MinUsableSignal minUsableSignal) {
        log.debug("Updating MinUsableSignal configuration");
        return update(minUsableSignal);
    }
}
