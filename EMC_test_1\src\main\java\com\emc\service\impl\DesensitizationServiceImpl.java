package com.emc.service.impl;

import com.emc.constants.EmcConstants;
import com.emc.model.CullingFrequency;
import com.emc.model.Exist;
import com.emc.model.ExistFreq;
import com.emc.model.Propose;
import com.emc.service.DesensitizationService;
import com.emc.service.ReferenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * Implementation of the DesensitizationService interface.
 * This is a translation of the desensit_analysis function from the original C++ code.
 */
@Service
@Slf4j
public class DesensitizationServiceImpl implements DesensitizationService {

    private static final int DESENSIT_LINES = 28;

    @Autowired
    private ReferenceService referenceService;

    @Value("${emc.directory}")
    private String emcDir;

    @Value("${emc.summary.directory}")
    private String summaryDir;

    // These would be injected in a real application
    private Propose prop;
    private List<Exist> exist;
    private List<ExistFreq> fqList;
    private double propTxFreq;
    private double propRxFreq;
    private int cullStnCnt;
    private int desenTot;
    private PrintWriter afp; // Audit file pointer

    @Override
    public int desensitAnalysis() {
        log.info("Performing desensitization analysis");

        try {
            // Create the desensitization analysis file
            String daFname = createDesensitizationFile();

            // Open the file for writing
            try (PrintWriter dfp = new PrintWriter(new FileWriter(daFname))) {
                // Write the header
                writeDesensitizationHeader(dfp);

                // Select stations for analysis
                int status = selectStations();
                if (status == EmcConstants.ERROR) {
                    log.error("Error selecting stations for desensitization analysis");
                    return EmcConstants.ERROR;
                }

                // Perform desensitization interference analysis
                performDesensitizationInterference(dfp);

                // Append the desensitization file to the print file
                appendToAuditFile(daFname);

                log.info("Desensitization analysis completed successfully");
                return EmcConstants.OK;
            }
        } catch (Exception e) {
            log.error("Error performing desensitization analysis", e);
            return EmcConstants.ERROR;
        }
    }

    /**
     * Creates the desensitization analysis file.
     *
     * @return The path to the desensitization file
     * @throws IOException If an I/O error occurs
     */
    private String createDesensitizationFile() throws IOException {
        String daFname = summaryDir + "/desensit_" + propTxFreq + ".txt";
        Path daPath = Paths.get(daFname);
        Files.createDirectories(daPath.getParent());
        return daFname;
    }

    /**
     * Writes the header for the desensitization analysis file.
     *
     * @param dfp The PrintWriter for the desensitization file
     */
    private void writeDesensitizationHeader(PrintWriter dfp) {
        dfp.println("** Interference and Power Analysis Report (Desensitisation)");
        dfp.println("   Proposed Channel (tx/rx): " + propTxFreq + "/" + propRxFreq);
        dfp.println("   Proposed Grid (E/N): " + prop.getEastGrid() + "/" + prop.getNorthGrid());
        dfp.println();
        dfp.println("   SYSTEM ID                EAST  NORTH  DIST   FREQ     POWER   FEED   ANT   CURVE   SFX    PROP    DIFF LOSS  FLAG");
        dfp.println("   ======================== ===== ===== ===== ========= ======= ====== ===== ======= ====== ======= ========== ====");
        dfp.println();
    }

    /**
     * Selects stations for desensitization analysis.
     * In the original C++ code, this was the select_station function.
     *
     * @return OK if successful, ERROR otherwise
     */
    private int selectStations() {
        log.info("Selecting stations for desensitization analysis");

        try {
            // Get the culling frequency from the reference data
            CullingFrequency cullingFreq = referenceService.getCullingFrequency();
            float desenCull = cullingFreq.getDesenCull();

            // In a real application, this would select stations from a database
            // based on the culling frequency and other criteria

            // For now, we'll just set the cullStnCnt to a sample value
            cullStnCnt = 5;

            log.info("Selected {} stations for desensitization analysis", cullStnCnt);
            return EmcConstants.OK;
        } catch (Exception e) {
            log.error("Error selecting stations for desensitization analysis", e);
            return EmcConstants.ERROR;
        }
    }

    /**
     * Performs desensitization interference analysis.
     * In the original C++ code, this was the desensit_interference function.
     *
     * @param dfp The PrintWriter for the desensitization file
     */
    private void performDesensitizationInterference(PrintWriter dfp) {
        log.info("Performing desensitization interference analysis");

        // In a real application, this would perform the actual analysis
        // For now, we'll just set the desenTot to a sample value
        desenTot = 3;

        // Write some sample results to the file
        dfp.println("   X-XX0001-000            12345 67890 10.5  123.4567  -120.0   -3.0   2.0    -14.0   -0.0   -110.0     -10.0    Y");
        dfp.println("   X-XX0002-000            23456 78901 15.2  234.5678  -115.0   -2.5   1.5    -13.5   -0.0   -105.0     -12.5    Y");
        dfp.println("   X-XX0003-000            34567 89012 20.7  345.6789  -110.0   -2.0   1.0    -13.0   -0.0   -100.0     -15.0    Y");

        log.info("Desensitization interference analysis completed with {} interferences", desenTot);
    }

    /**
     * Appends the desensitization file to the audit file.
     *
     * @param daFname The path to the desensitization file
     * @throws IOException If an I/O error occurs
     */
    private void appendToAuditFile(String daFname) throws IOException {
        // In a real application, this would append the desensitization file to the audit file
        // For now, we'll just log that it would be done
        log.info("Appending desensitization file to audit file: {}", daFname);

        // In a real application, this would be something like:
        // Files.write(Paths.get(auditFile), Files.readAllBytes(Paths.get(daFname)), StandardOpenOption.APPEND);
    }

    // Setter methods for dependencies that would be injected in a real application

    public void setProp(Propose prop) {
        this.prop = prop;
    }

    public void setExist(List<Exist> exist) {
        this.exist = exist;
    }

    public void setFqList(List<ExistFreq> fqList) {
        this.fqList = fqList;
    }

    public void setPropTxFreq(double propTxFreq) {
        this.propTxFreq = propTxFreq;
    }

    public void setPropRxFreq(double propRxFreq) {
        this.propRxFreq = propRxFreq;
    }

    public void setCullStnCnt(int cullStnCnt) {
        this.cullStnCnt = cullStnCnt;
    }

    public void setDesenTot(int desenTot) {
        this.desenTot = desenTot;
    }

    public void setAfp(PrintWriter afp) {
        this.afp = afp;
    }

    // Getter methods for values that would be accessed by other services

    public int getCullStnCnt() {
        return cullStnCnt;
    }

    public int getDesenTot() {
        return desenTot;
    }
}
