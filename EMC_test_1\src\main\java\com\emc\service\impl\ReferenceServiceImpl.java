package com.emc.service.impl;

import com.emc.constants.EmcConstants;
import com.emc.dao.SubDistrictDao;
import com.emc.dao.OffChannelRejectionDao;
import com.emc.dao.MinUsableSignalDao;
import com.emc.dao.CullingFrequencyDao;
import com.emc.dao.TerrainPointDao;
import com.emc.model.SubDistrict;
import com.emc.model.OffChannelRejection;
import com.emc.model.MinUsableSignal;
import com.emc.model.CullingFrequency;
import com.emc.model.TerrainPoint;
import com.emc.service.ReferenceService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Implementation of the ReferenceService interface.
 * This is a translation of the load_reference function from the original C++ code.
 */
@Service
@Slf4j
@Data
public class ReferenceServiceImpl implements ReferenceService {

    // DAO dependencies
    private final SubDistrictDao subDistrictDao;
    private final OffChannelRejectionDao offChannelRejectionDao;
    private final MinUsableSignalDao minUsableSignalDao;
    private final CullingFrequencyDao cullingFrequencyDao;
    private final TerrainPointDao terrainPointDao;

    // Reference data structures (cached for performance)
    private List<SubDistrict> subDistricts = new ArrayList<>();
    private List<OffChannelRejection> offChannelRejections = new ArrayList<>();
    private List<MinUsableSignal> minUsableSignals = new ArrayList<>();
    private List<TerrainPoint> fineTerrainEast = new ArrayList<>();
    private List<TerrainPoint> fineTerrainNorth = new ArrayList<>();
    private CullingFrequency cullingFrequency = new CullingFrequency();

    // Map for quick lookup of sub-districts by code
    private Map<String, SubDistrict> subDistrictMap = new HashMap<>();

    @Autowired
    public ReferenceServiceImpl(SubDistrictDao subDistrictDao,
                               OffChannelRejectionDao offChannelRejectionDao,
                               MinUsableSignalDao minUsableSignalDao,
                               CullingFrequencyDao cullingFrequencyDao,
                               TerrainPointDao terrainPointDao) {
        this.subDistrictDao = subDistrictDao;
        this.offChannelRejectionDao = offChannelRejectionDao;
        this.minUsableSignalDao = minUsableSignalDao;
        this.cullingFrequencyDao = cullingFrequencyDao;
        this.terrainPointDao = terrainPointDao;
    }

    @Override
    @Transactional(readOnly = true)
    public int loadReference() {
        log.info("Loading reference data using DAO layer");

        try {
            // Load sub-district information
            loadSubDistricts();

            // Load off-channel rejection information
            loadOffChannelRejections();

            // Load minimum usable signal information
            loadMinUsableSignals();

            // Load fine terrain information
            loadFineTerrainPoints();

            // Load culling frequencies
            loadCullingFrequencies();

            log.info("Reference data loaded successfully. SubDistricts: {}, OffChannelRejections: {}, MinUsableSignals: {}, TerrainPoints: {}",
                    subDistricts.size(), offChannelRejections.size(), minUsableSignals.size(),
                    fineTerrainEast.size() + fineTerrainNorth.size());
            return EmcConstants.OK;
        } catch (Exception e) {
            log.error("Error loading reference data", e);
            return EmcConstants.ERROR;
        }
    }

    /**
     * Loads sub-district information using DAO.
     */
    private void loadSubDistricts() {
        log.info("Loading sub-district information from database");

        // Load from database using DAO
        subDistricts = subDistrictDao.findAllOrderBySubDistrictCode();

        // Build the map for quick lookup
        subDistrictMap.clear();
        for (SubDistrict sd : subDistricts) {
            subDistrictMap.put(sd.getSubDistrictCode(), sd);
        }

        log.info("Loaded {} sub-districts", subDistricts.size());
    }

    /**
     * Loads off-channel rejection information using DAO.
     */
    private void loadOffChannelRejections() {
        log.info("Loading off-channel rejection information from database");

        // Load from database using DAO
        offChannelRejections = offChannelRejectionDao.findAllOrderByChannelSeparation();

        // If no data exists in database, create default data
        if (offChannelRejections.isEmpty()) {
            log.warn("No off-channel rejection data found in database, using default values");
            createDefaultOffChannelRejections();
        }

        log.info("Loaded {} off-channel rejections", offChannelRejections.size());
    }

    /**
     * Creates default off-channel rejection data if none exists in database.
     */
    private void createDefaultOffChannelRejections() {
        OffChannelRejection ocr1 = new OffChannelRejection();
        ocr1.setChannelSeparation(0.0125f);
        ocr1.setLowVhfRejection(80.0f);
        ocr1.setVhfRejection(80.0f);
        ocr1.setUhfRejection(80.0f);
        ocr1.setUhf800Rejection(80.0f);
        offChannelRejections.add(ocr1);

        OffChannelRejection ocr2 = new OffChannelRejection();
        ocr2.setChannelSeparation(0.025f);
        ocr2.setLowVhfRejection(60.0f);
        ocr2.setVhfRejection(60.0f);
        ocr2.setUhfRejection(60.0f);
        ocr2.setUhf800Rejection(60.0f);
        offChannelRejections.add(ocr2);
    }

    /**
     * Loads minimum usable signal information using DAO.
     */
    private void loadMinUsableSignals() {
        log.info("Loading minimum usable signal information from database");

        // Load from database using DAO
        minUsableSignals.clear();
        MinUsableSignal config = minUsableSignalDao.getConfiguration();
        minUsableSignals.add(config);

        log.info("Loaded minimum usable signal configuration");
    }

    /**
     * Loads fine terrain points using DAO.
     */
    private void loadFineTerrainPoints() {
        log.info("Loading fine terrain points from database");

        // Load all terrain points from database
        List<TerrainPoint> allTerrainPoints = terrainPointDao.findAll();

        // Clear existing data
        fineTerrainEast.clear();
        fineTerrainNorth.clear();

        // For now, we'll split terrain points arbitrarily between east and north
        // In a real implementation, this would depend on the actual terrain data structure
        for (int i = 0; i < allTerrainPoints.size(); i++) {
            if (i % 2 == 0) {
                fineTerrainEast.add(allTerrainPoints.get(i));
            } else {
                fineTerrainNorth.add(allTerrainPoints.get(i));
            }
        }

        // If no data exists, create some default terrain points
        if (allTerrainPoints.isEmpty()) {
            log.warn("No terrain data found in database, using default values");
            createDefaultTerrainPoints();
        }

        log.info("Loaded {} fine terrain points (east: {}, north: {})",
                fineTerrainEast.size() + fineTerrainNorth.size(),
                fineTerrainEast.size(), fineTerrainNorth.size());
    }

    /**
     * Creates default terrain points if none exist in database.
     */
    private void createDefaultTerrainPoints() {
        TerrainPoint tp1 = new TerrainPoint();
        tp1.setEastGrid(12345);
        tp1.setNorthGrid(67890);
        tp1.setHeight(100);
        fineTerrainEast.add(tp1);
        fineTerrainNorth.add(tp1);
    }

    /**
     * Loads culling frequencies using DAO.
     */
    private void loadCullingFrequencies() {
        log.info("Loading culling frequencies from database");

        // Load from database using DAO
        cullingFrequency = cullingFrequencyDao.getConfiguration();

        log.info("Loaded culling frequencies: desen={}, intmod2={}, intmod3={}",
                cullingFrequency.getDesenCull(),
                cullingFrequency.getIntmod2Cull(),
                cullingFrequency.getIntmod3Cull());
    }

    /**
     * Gets a sub-district by code.
     *
     * @param code The sub-district code
     * @return The sub-district, or null if not found
     */
    @Override
    public SubDistrict getSubDistrictByCode(String code) {
        return subDistrictMap.get(code);
    }

    /**
     * Gets district information for a sub-district.
     *
     * @param subDistrict The sub-district code
     * @param distType Output parameter for the district type
     * @param noiseCode Output parameter for the noise code
     * @param distIndex Output parameter for the district index
     * @return OK if successful, ERROR otherwise
     */
    @Override
    public int getDistrict(String subDistrict, char[] distType, int noiseCode, int distIndex) {
        SubDistrict sd = getSubDistrictByCode(subDistrict);
        if (sd == null) {
            return EmcConstants.ERROR;
        }

        distType[0] = sd.getDistType().charAt(0);
        noiseCode = sd.getNoiseCode();
        distIndex = 0; // Not used in this implementation

        return EmcConstants.OK;
    }
}
