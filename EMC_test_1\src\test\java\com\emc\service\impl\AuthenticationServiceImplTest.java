package com.emc.service.impl;

import com.emc.constants.EmcConstants;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class AuthenticationServiceImplTest {

    @Test
    void testUserLogin_Success() {
        // Setup
        AuthenticationServiceImpl authenticationService = new AuthenticationServiceImpl();
        StringBuilder errMsg = new StringBuilder();
        
        // Execute
        int result = authenticationService.userLogin("admin", "password", errMsg);
        
        // Verify
        assertEquals(EmcConstants.LOGIN_OK, result);
        assertEquals(0, errMsg.length());
    }

    @Test
    void testUserLogin_Failure() {
        // Setup
        AuthenticationServiceImpl authenticationService = new AuthenticationServiceImpl();
        StringBuilder errMsg = new StringBuilder();
        
        // Execute
        int result = authenticationService.userLogin("admin", "wrong_password", errMsg);
        
        // Verify
        assertEquals(EmcConstants.ERROR, result);
        assertTrue(errMsg.length() > 0);
        assertEquals("Invalid username or password", errMsg.toString());
    }

    @Test
    void testUserLogout() {
        // Setup
        AuthenticationServiceImpl authenticationService = new AuthenticationServiceImpl();
        
        // Execute and Verify (no exceptions should be thrown)
        assertDoesNotThrow(() -> authenticationService.userLogout());
    }
}
