package com.emc.model;

import lombok.Data;

/**
 * Java equivalent of the EXIST struct from the original C++ code.
 * Represents an existing station for EMC analysis.
 */
@Data
public class Exist {
    private String emcUid;
    private char sysCategory;
    private String sysType;
    private String sysNo;
    private String sysSuffix;
    private int eastGrid;
    private int northGrid;
    private String subDistrict;
    private char stationType;
    private double desenAttDb;
    private double intmodAttDb;
    private String antenna;
    private int antHeight;
    private double pwDbw;
    private int azMaxRad;
    private double azMaxRadR;
    private int feedLoss;
    private String sfxFilter;
    private double heightAsl;
    private char distType;
    private int noiseCode;
    private int distIndex;
}
