package com.emc.dao.impl;

import com.emc.dao.OffChannelRejectionDao;
import com.emc.dao.mapper.OffChannelRejectionRowMapper;
import com.emc.model.OffChannelRejection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.Optional;

/**
 * JDBC implementation of OffChannelRejectionDao.
 */
@Repository
@Slf4j
public class OffChannelRejectionDaoImpl implements OffChannelRejectionDao {
    
    private final JdbcTemplate jdbcTemplate;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private final OffChannelRejectionRowMapper rowMapper;
    
    // SQL queries
    private static final String SELECT_ALL = 
        "SELECT ID, CHANNEL_SEPARATION, LOW_VHF_REJECTION, VHF_REJECTION, UHF_REJECTION, UHF_800_REJECTION " +
        "FROM OFF_CHANNEL_TAB";
    
    private static final String SELECT_BY_ID = SELECT_ALL + " WHERE ID = ?";
    private static final String SELECT_BY_CHANNEL_SEP = SELECT_ALL + " WHERE CHANNEL_SEPARATION = ?";
    private static final String SELECT_BY_CHANNEL_SEP_GTE = SELECT_ALL + " WHERE CHANNEL_SEPARATION >= ?";
    private static final String SELECT_BY_CHANNEL_SEP_LTE = SELECT_ALL + " WHERE CHANNEL_SEPARATION <= ?";
    private static final String SELECT_ALL_ORDERED = SELECT_ALL + " ORDER BY CHANNEL_SEPARATION";
    
    private static final String INSERT = 
        "INSERT INTO OFF_CHANNEL_TAB (CHANNEL_SEPARATION, LOW_VHF_REJECTION, VHF_REJECTION, UHF_REJECTION, UHF_800_REJECTION) " +
        "VALUES (?, ?, ?, ?, ?)";
    
    private static final String UPDATE = 
        "UPDATE OFF_CHANNEL_TAB SET CHANNEL_SEPARATION = ?, LOW_VHF_REJECTION = ?, VHF_REJECTION = ?, " +
        "UHF_REJECTION = ?, UHF_800_REJECTION = ? WHERE ID = ?";
    
    private static final String DELETE = "DELETE FROM OFF_CHANNEL_TAB WHERE ID = ?";
    private static final String EXISTS = "SELECT COUNT(*) FROM OFF_CHANNEL_TAB WHERE ID = ?";
    private static final String COUNT = "SELECT COUNT(*) FROM OFF_CHANNEL_TAB";
    
    @Autowired
    public OffChannelRejectionDaoImpl(JdbcTemplate jdbcTemplate, NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.rowMapper = new OffChannelRejectionRowMapper();
    }
    
    @Override
    public OffChannelRejection save(OffChannelRejection entity) {
        log.debug("Saving OffChannelRejection with channel separation: {}", entity.getChannelSeparation());
        
        KeyHolder keyHolder = new GeneratedKeyHolder();
        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(INSERT, Statement.RETURN_GENERATED_KEYS);
            ps.setFloat(1, entity.getChannelSeparation());
            ps.setFloat(2, entity.getLowVhfRejection());
            ps.setFloat(3, entity.getVhfRejection());
            ps.setFloat(4, entity.getUhfRejection());
            ps.setFloat(5, entity.getUhf800Rejection());
            return ps;
        }, keyHolder);
        
        // Note: This assumes the entity has an ID field that we're not currently using
        // If needed, you can add an ID field to the OffChannelRejection model
        
        return entity;
    }
    
    @Override
    public OffChannelRejection update(OffChannelRejection entity) {
        log.debug("Updating OffChannelRejection with channel separation: {}", entity.getChannelSeparation());
        
        // Note: This implementation assumes we have an ID field
        // For now, we'll update based on channel separation as it might be unique
        String updateByChannelSep = 
            "UPDATE OFF_CHANNEL_TAB SET LOW_VHF_REJECTION = ?, VHF_REJECTION = ?, " +
            "UHF_REJECTION = ?, UHF_800_REJECTION = ? WHERE CHANNEL_SEPARATION = ?";
        
        int rowsAffected = jdbcTemplate.update(updateByChannelSep,
            entity.getLowVhfRejection(),
            entity.getVhfRejection(),
            entity.getUhfRejection(),
            entity.getUhf800Rejection(),
            entity.getChannelSeparation()
        );
        
        if (rowsAffected == 0) {
            throw new RuntimeException("OffChannelRejection not found for update with channel separation: " + entity.getChannelSeparation());
        }
        
        return entity;
    }
    
    @Override
    public Optional<OffChannelRejection> findById(Long id) {
        log.debug("Finding OffChannelRejection by ID: {}", id);
        try {
            OffChannelRejection rejection = jdbcTemplate.queryForObject(SELECT_BY_ID, rowMapper, id);
            return Optional.ofNullable(rejection);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    @Override
    public List<OffChannelRejection> findAll() {
        log.debug("Finding all OffChannelRejections");
        return jdbcTemplate.query(SELECT_ALL, rowMapper);
    }
    
    @Override
    public boolean deleteById(Long id) {
        log.debug("Deleting OffChannelRejection: {}", id);
        int rowsAffected = jdbcTemplate.update(DELETE, id);
        return rowsAffected > 0;
    }
    
    @Override
    public boolean existsById(Long id) {
        Integer count = jdbcTemplate.queryForObject(EXISTS, Integer.class, id);
        return count != null && count > 0;
    }
    
    @Override
    public long count() {
        Integer count = jdbcTemplate.queryForObject(COUNT, Integer.class);
        return count != null ? count : 0;
    }
    
    @Override
    public Optional<OffChannelRejection> findByChannelSeparation(float channelSeparation) {
        log.debug("Finding OffChannelRejection by channel separation: {}", channelSeparation);
        try {
            OffChannelRejection rejection = jdbcTemplate.queryForObject(SELECT_BY_CHANNEL_SEP, rowMapper, channelSeparation);
            return Optional.ofNullable(rejection);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    @Override
    public List<OffChannelRejection> findByChannelSeparationGreaterThanEqual(float channelSeparation) {
        log.debug("Finding OffChannelRejections with channel separation >= {}", channelSeparation);
        return jdbcTemplate.query(SELECT_BY_CHANNEL_SEP_GTE, rowMapper, channelSeparation);
    }
    
    @Override
    public List<OffChannelRejection> findByChannelSeparationLessThanEqual(float channelSeparation) {
        log.debug("Finding OffChannelRejections with channel separation <= {}", channelSeparation);
        return jdbcTemplate.query(SELECT_BY_CHANNEL_SEP_LTE, rowMapper, channelSeparation);
    }
    
    @Override
    public List<OffChannelRejection> findAllOrderByChannelSeparation() {
        log.debug("Finding all OffChannelRejections ordered by channel separation");
        return jdbcTemplate.query(SELECT_ALL_ORDERED, rowMapper);
    }
}
