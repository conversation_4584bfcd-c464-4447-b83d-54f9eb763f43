package com.emc.service.impl;

import com.emc.constants.EmcConstants;
import com.emc.service.AuthenticationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Implementation of the AuthenticationService interface.
 */
@Service
@Slf4j
public class AuthenticationServiceImpl implements AuthenticationService {
    
    @Override
    public int userLogin(String username, String password, StringBuilder errMsg) {
        log.info("User login attempt: {}", username);
        // In a real application, this would authenticate against a user database
        if ("admin".equals(username) && "password".equals(password)) {
            return EmcConstants.LOGIN_OK;
        } else {
            errMsg.append("Invalid username or password");
            return EmcConstants.ERROR;
        }
    }
    
    @Override
    public void userLogout() {
        log.info("User logout");
        // In a real application, this would perform logout operations
    }
}
