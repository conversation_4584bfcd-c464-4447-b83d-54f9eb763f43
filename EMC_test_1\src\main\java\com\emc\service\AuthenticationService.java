package com.emc.service;

/**
 * Service for user authentication.
 */
public interface AuthenticationService {
    
    /**
     * Performs user login.
     * Equivalent to the user_login function in the original C++ code.
     * 
     * @param username The username
     * @param password The password
     * @param errMsg Output parameter for error messages
     * @return LOGIN_OK if successful, error code otherwise
     */
    int userLogin(String username, String password, StringBuilder errMsg);
    
    /**
     * Performs user logout.
     * Equivalent to the user_logout function in the original C++ code.
     */
    void userLogout();
}
