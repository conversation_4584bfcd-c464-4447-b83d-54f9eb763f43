package com.emc.dao.mapper;

import com.emc.model.CullingFrequency;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for CullingFrequency entity.
 */
public class CullingFrequencyRowMapper implements RowMapper<CullingFrequency> {
    
    @Override
    public CullingFrequency mapRow(ResultSet rs, int rowNum) throws SQLException {
        CullingFrequency cullingFrequency = new CullingFrequency();
        cullingFrequency.setDesenCull(rs.getFloat("DESEN_CULL"));
        cullingFrequency.setIntmod2Cull(rs.getFloat("INTMOD2_CULL"));
        cullingFrequency.setIntmod3Cull(rs.getFloat("INTMOD3_CULL"));
        return cullingFrequency;
    }
}
