package com.emc.service.impl;

import com.emc.constants.EmcConstants;
import com.emc.model.*;
import com.emc.service.*;
import com.emc.util.DateTimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * Implementation of the EmcAnalysisService.
 * This is a translation of the main function in esemba0x.c.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EmcAnalysisServiceImpl implements EmcAnalysisService {

    @Value("${emc.directory}")
    private String emcDir;

    @Value("${emc.batch.directory}")
    private String batchDir;

    @Value("${emc.audit.directory}")
    private String auditDir;

    @Value("${emc.summary.directory}")
    private String summaryDir;

    @Value("${emc.interactive.directory}")
    private String interactiveDir;

    private static final String DEFAULT_EMC_UID = "ELSO_WEB.ANALYSIS";

    private final ReferenceService referenceService;
    private final TerrainService terrainService;
    private final DesensitizationService desensitizationService;
    private final IntermodulationService intermodulationService;
    private final CochannelService cochannelService;

    // Global variables from the original code
    private Propose prop;
    private List<Exist> exist;
    private List<ExistFreq> fqList;
    private List<ExistFreq> sFqList;
    private int fqCnt;
    private int propStnCnt;
    private int propFqCnt;
    private double propTxFreq;
    private double propRxFreq;
    private double existTxFreq;
    private double existRxFreq;
    private int eIdx;
    private int[] prevTxGrid = new int[2];
    private int[] prevRxGrid = new int[2];
    private double prevDiffLoss;
    private int cochanTot;
    private int desenTot;
    private int intmod2VictTot;
    private int intmod2Tx1Tot;
    private int intmod2Tx2Tot;
    private int intmod3VictTot;
    private int intmod3Tx1Tot;
    private int intmod3Tx3Tot;
    private int cullStnCnt;
    private String emcUid;
    private String printFile;
    private String printerID;
    private StringBuilder sysDate = new StringBuilder();
    private StringBuilder sysTime = new StringBuilder();
    private StringBuilder yymmdd = new StringBuilder();
    private StringBuilder hhmmss = new StringBuilder();
    private PrintWriter afp; // Audit file pointer

    /**
     * Performs EMC analysis based on the provided request.
     *
     * @param request The EMC analysis request
     * @return The result of the EMC analysis
     */
    @Override
    public EmcAnalysisResult performAnalysis(EmcAnalysisRequest request) {
        EmcAnalysisResult result = new EmcAnalysisResult();
        result.setEmcUid(request.getEmcUid());
        result.setStatus("PROCESSING");
        result.setWarnings(new ArrayList<>());
        result.setErrors(new ArrayList<>());

        try {
            // Initialize data structures
            initializeDataStructures();

            // Set up file paths
            boolean interactive = request.isInteractive();
            String emcBatch = setupFilePaths(request, interactive);
            result.setPrintFile(printFile);

            // Write batch content to file if provided
            if (request.getBatchContent() != null && !request.getBatchContent().isEmpty()) {
                writeBatchFile(emcBatch, request.getBatchContent());
            }

            // Open batch file
            BufferedReader bfp = openBatchFile(emcBatch, result);
            if (bfp == null) {
                return result;
            }

            // Open audit file
            String audit = setupAuditFile();
            result.setAuditFile(audit);

            // Load reference data
            referenceService.loadReference();

            // Process batch file
            processEmcBatch(bfp, result);

            // Clean up
            cleanUp(interactive, request.getInteractiveMode(), emcBatch);

            result.setStatus("COMPLETED");
            return result;

        } catch (Exception e) {
            log.error("Error performing EMC analysis", e);
            result.setStatus("ERROR");
            result.setMessage("Error performing EMC analysis: " + e.getMessage());
            return result;
        }
    }

    /**
     * Initializes the data structures used in the EMC analysis.
     */
    private void initializeDataStructures() {
        prop = new Propose();
        exist = new ArrayList<>(EmcConstants.MAX_EXIST);
        fqList = new ArrayList<>(EmcConstants.MAX_EXIST * 2 + 500);
        sFqList = new ArrayList<>(EmcConstants.MAX_EXIST * 2 + 500);

        for (int i = 0; i < EmcConstants.MAX_EXIST; i++) {
            exist.add(null);
        }

        for (int i = 0; i < EmcConstants.MAX_EXIST * 2 + 500; i++) {
            fqList.add(null);
            sFqList.add(null);
        }

        fqCnt = 0;
    }

    /**
     * Sets up file paths based on the request.
     *
     * @param request The EMC analysis request
     * @param interactive Whether this is an interactive analysis
     * @return The path to the EMC batch file
     */
    private String setupFilePaths(EmcAnalysisRequest request, boolean interactive) {
        String emcBatch;

        // Get current date and time
        DateTimeUtils.getSysDateTime(sysDate, sysTime, yymmdd, hhmmss);

        // Set up print file
        if (interactive) {
            // Use the default EMC UID for interactive mode if not provided
            String emcUid = (request.getEmcUid() != null && !request.getEmcUid().isEmpty())
                ? request.getEmcUid() : DEFAULT_EMC_UID;

            printFile = request.getPrintFile() + "." + emcUid;
            emcBatch = interactiveDir + "/" + emcUid;
        } else {
            // Use EMC UID or default for batch mode as well
            String emcUid = (request.getEmcUid() != null && !request.getEmcUid().isEmpty())
                ? request.getEmcUid() : DEFAULT_EMC_UID;
            printFile = request.getPrintFile() + "." + emcUid;
            emcBatch = batchDir + "/" + yymmdd;
        }

        printerID = request.getPrinterId();

        // Remove last EMC print file if it exists
        try {
            Files.deleteIfExists(Paths.get(printFile));
        } catch (IOException e) {
            log.warn("Failed to delete previous print file: {}", e.getMessage());
        }

        return emcBatch;
    }

    /**
     * Writes batch content to a file.
     *
     * @param emcBatch The path to the EMC batch file
     * @param batchContent The content to write
     * @throws IOException If an I/O error occurs
     */
    private void writeBatchFile(String emcBatch, String batchContent) throws IOException {
        Path batchPath = Paths.get(emcBatch);
        Files.createDirectories(batchPath.getParent());
        Files.write(batchPath, batchContent.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * Opens the batch file for reading.
     *
     * @param emcBatch The path to the EMC batch file
     * @param result The result object to update with errors
     * @return A BufferedReader for the batch file, or null if an error occurs
     */
    private BufferedReader openBatchFile(String emcBatch, EmcAnalysisResult result) {
        try {
            return new BufferedReader(new FileReader(emcBatch));
        } catch (FileNotFoundException e) {
            log.error("Failed to open EMC batch file: {}", emcBatch, e);
            result.setStatus("ERROR");
            result.setMessage("Fatal error: fail to open EMC batch: " + emcBatch);
            return null;
        }
    }

    /**
     * Sets up the audit file.
     *
     * @return The path to the audit file
     * @throws IOException If an I/O error occurs
     */
    private String setupAuditFile() throws IOException {
        String audit = auditDir + "/" + yymmdd;
        Path auditPath = Paths.get(audit);
        Files.createDirectories(auditPath.getParent());
        afp = new PrintWriter(new FileWriter(audit));
        return audit;
    }



    /**
     * Processes the EMC batch file.
     *
     * @param bfp The batch file reader
     * @param result The result object to update
     * @throws IOException If an I/O error occurs
     */
    private void processEmcBatch(BufferedReader bfp, EmcAnalysisResult result) throws IOException {
        String instr = bfp.readLine();
        if (instr == null) {
            result.setStatus("ERROR");
            result.setMessage("Empty EMC batch");
            return;
        }

        // Process each line in the batch file
        int i = 0;
        do {
            // Create new Exist object if needed
            if (exist.get(i) == null) {
                exist.set(i, new Exist());
            }

            // Parse the line and populate the Exist object
            parseExistLine(instr, i);

            // Get local terrain height
            exist.get(i).setHeightAsl(terrainService.getLocalHeight(exist.get(i).getEastGrid() / 10.0f,
                                                                  exist.get(i).getNorthGrid() / 10.0f));

            // Get district information
            int[] distInfo = new int[2];
            char[] distType = new char[1];
            int status = getDistrict(exist.get(i).getSubDistrict(), distType, distInfo[0], distInfo[1]);
            if (status == EmcConstants.ERROR) {
                log.warn("Error: sub-district code not found in SUBDISTRICT_TAB: {}",
                        exist.get(i).getSubDistrict());
                continue;
            }

            exist.get(i).setDistType(distType[0]);
            exist.get(i).setNoiseCode(distInfo[0]);
            exist.get(i).setDistIndex(distInfo[1]);

            i++;
        } while ((instr = bfp.readLine()) != null);

        propStnCnt = i;
        propFqCnt = fqCnt;

        // Process each frequency in the list
        processFrequencies(result);
    }

    /**
     * Parses a line from the batch file and populates an Exist object.
     *
     * @param instr The line to parse
     * @param i The index of the Exist object to populate
     */
    private void parseExistLine(String instr, int i) {
        Exist existObj = exist.get(i);

        // Extract EMC UID (first 19 characters)
        existObj.setEmcUid(instr.substring(0, 19).trim());

        // Extract east grid (characters 19-24)
        String tmpGrid = instr.substring(19, 24).trim();
        existObj.setEastGrid(Integer.parseInt(tmpGrid));

        // Extract north grid (characters 24-29)
        tmpGrid = instr.substring(24, 29).trim();
        existObj.setNorthGrid(Integer.parseInt(tmpGrid));

        // Set default system information
        existObj.setSysCategory('X');
        existObj.setSysType("XX");
        existObj.setSysNo(String.format("%04d", i));
        existObj.setSysSuffix("000");

        // Extract sub-district (characters 29-32)
        existObj.setSubDistrict(instr.substring(29, 32).trim());

        // Extract station type (character 32)
        existObj.setStationType(instr.charAt(32));

        // Extract antenna type (characters 33-35)
        existObj.setAntenna(instr.substring(33, 35).trim());

        // Extract azimuth of max radiation (characters 35-38)
        String tmpAz = instr.substring(35, 38).trim();
        existObj.setAzMaxRad(Integer.parseInt(tmpAz));
        existObj.setAzMaxRadR((float) existObj.getAzMaxRad() / 180 * Math.PI);

        // Extract antenna height (characters 38-41)
        String tmpHt = instr.substring(38, 41).trim();
        existObj.setAntHeight(Integer.parseInt(tmpHt));

        // Extract power in dBW (characters 41-46)
        char pwSign = instr.charAt(41);
        String tmpPwDbw = instr.substring(42, 46).trim();
        existObj.setPwDbw(Double.parseDouble(tmpPwDbw));
        if (pwSign == '-') {
            existObj.setPwDbw(existObj.getPwDbw() * (-1));
        }

        // Extract feed loss (characters 46-49)
        String tmpFeed = instr.substring(46, 49).trim();
        existObj.setFeedLoss(Integer.parseInt(tmpFeed));

        // Extract desensitization attenuation (characters 49-54)
        char attSign = instr.charAt(49);
        String tmpAttDb = instr.substring(50, 54).trim();
        existObj.setDesenAttDb(Double.parseDouble(tmpAttDb));
        if (attSign == '-') {
            existObj.setDesenAttDb(existObj.getDesenAttDb() * (-1));
        }

        // Extract intermodulation attenuation (characters 54-59)
        attSign = instr.charAt(54);
        tmpAttDb = instr.substring(55, 59).trim();
        existObj.setIntmodAttDb(Double.parseDouble(tmpAttDb));
        if (attSign == '-') {
            existObj.setIntmodAttDb(existObj.getIntmodAttDb() * (-1));
        }

        // Extract SFX filter (characters 59-69)
        existObj.setSfxFilter(instr.substring(59, 69).trim());

        // Extract frequencies
        for (int k = 69; k < instr.length(); ) {
            if (fqList.get(fqCnt) == null) {
                fqList.set(fqCnt, new ExistFreq());
                sFqList.set(fqCnt, new ExistFreq());
            }

            // Extract TX frequency
            String tmpFreq = instr.substring(k, k + 11).trim();
            double txFreq = Double.parseDouble(tmpFreq);
            fqList.get(fqCnt).setTxFreq(txFreq);
            sFqList.get(fqCnt).setTxFreq(txFreq);

            int txChannel = (int) (txFreq / EmcConstants.MIN_CHANNEL_SEP + 0.5);
            fqList.get(fqCnt).setTxChannel(txChannel);
            sFqList.get(fqCnt).setTxChannel(txChannel);

            k += 11;

            // Extract RX frequency
            tmpFreq = instr.substring(k, k + 11).trim();
            double rxFreq = Double.parseDouble(tmpFreq);
            fqList.get(fqCnt).setRxFreq(rxFreq);
            sFqList.get(fqCnt).setRxFreq(rxFreq);

            int rxChannel = (int) (rxFreq / EmcConstants.MIN_CHANNEL_SEP + 0.5);
            fqList.get(fqCnt).setRxChannel(rxChannel);
            sFqList.get(fqCnt).setRxChannel(rxChannel);

            // Set station node
            fqList.get(fqCnt).setStnNode(i);
            sFqList.get(fqCnt).setStnNode(i);

            // Set global variables
            existTxFreq = fqList.get(fqCnt).getTxFreq();
            existRxFreq = fqList.get(fqCnt).getRxFreq();
            eIdx = i;

            k += 11;
            fqCnt++;

            // Check if we've reached the end of the line
            if (k >= instr.length() || instr.charAt(k) == '\n') {
                break;
            }
        }
    }

    /**
     * Gets district information from the reference service.
     *
     * @param subDistrict The sub-district code
     * @param distType The district type (output)
     * @param noiseCode The noise code (output)
     * @param distIndex The district index (output)
     * @return EmcConstants.OK if successful, EmcConstants.ERROR otherwise
     */
    private int getDistrict(String subDistrict, char[] distType, int noiseCode, int distIndex) {
        return referenceService.getDistrict(subDistrict, distType, noiseCode, distIndex);
    }

    /**
     * Processes each frequency in the list.
     *
     * @param result The result object to update
     * @throws IOException If an I/O error occurs
     */
    private void processFrequencies(EmcAnalysisResult result) throws IOException {
        int prevStnNode = -1;

        for (int j = 0; j < propFqCnt; j++) {
            if (prevStnNode != fqList.get(j).getStnNode()) {
                prevStnNode = prop.setStnNode(fqList.get(j).getStnNode());
                int i = fqList.get(j).getStnNode();

                // Copy data from exist to prop
                copyExistToProp(i);
            }

            // Set frequencies and channels
            propTxFreq = fqList.get(j).getTxFreq();
            propRxFreq = fqList.get(j).getRxFreq();
            prop.setTxChannel(fqList.get(j).getTxChannel());
            prop.setRxChannel(fqList.get(j).getRxChannel());

            // Set band mode
            setBandMode(EmcConstants.IS_PROPOSED);

            // Get current date and time
            DateTimeUtils.getSysDateTime(sysDate, sysTime, yymmdd, hhmmss);

            // Write to audit file
            writeAuditHeader();

            // Reset counters
            resetCounters();

            // Perform co-channel analysis
            cochannelService.cochaninf();

            // Perform desensitization analysis
            int status = desensitizationService.desensitAnalysis();
            if (status == EmcConstants.ERROR) {
                continue;
            }

            if (cullStnCnt == 0) {
                printEmcSummary();
                continue;
            }

            // Perform intermodulation analysis
            intermodulationService.intermod2();
            intermodulationService.intermod3();

            afp.println("\n\n\n");

            // Print EMC summary
            printEmcSummary();
        }

        // Update result with summary statistics
        updateResultStatistics(result);
    }

    /**
     * Sets the band mode based on the station type.
     *
     * @param stationType The station type
     */
    private void setBandMode(char stationType) {
        // Implementation depends on the specific requirements
        // This is a placeholder
    }

    /**
     * Copies data from an Exist object to the Prop object.
     *
     * @param i The index of the Exist object to copy from
     */
    private void copyExistToProp(int i) {
        Exist existObj = exist.get(i);

        emcUid = existObj.getEmcUid();
        prop.setSysCategory(existObj.getSysCategory());
        prop.setSysType(existObj.getSysType());
        prop.setSysNo(existObj.getSysNo());
        prop.setSysSuffix(existObj.getSysSuffix());
        prop.setEastGrid(existObj.getEastGrid());
        prop.setNorthGrid(existObj.getNorthGrid());
        prop.setSubDistrict(existObj.getSubDistrict());
        prop.setStationType(existObj.getStationType());
        prop.setDesenAttDb(existObj.getDesenAttDb());
        prop.setIntmodAttDb(existObj.getIntmodAttDb());
        prop.setAntenna(existObj.getAntenna());
        prop.setAntHeight(existObj.getAntHeight());
        prop.setPwDbw(existObj.getPwDbw());
        prop.setAzMaxRad(existObj.getAzMaxRad());
        prop.setAzMaxRadR(existObj.getAzMaxRadR());
        prop.setFeedLoss(existObj.getFeedLoss());
        prop.setSfxFilter(existObj.getSfxFilter());
        prop.setHeightAsl(existObj.getHeightAsl());
        prop.setDistType(existObj.getDistType());
        prop.setNoiseCode(existObj.getNoiseCode());
        prop.setDistIndex(existObj.getDistIndex());
    }

    /**
     * Resets counters for a new analysis.
     */
    private void resetCounters() {
        prevTxGrid[0] = prevTxGrid[1] = 0;
        prevRxGrid[0] = prevRxGrid[1] = 0;
        prevDiffLoss = 0.0;

        cochanTot = 0;
        desenTot = 0;
        intmod2VictTot = 0;
        intmod2Tx1Tot = 0;
        intmod2Tx2Tot = 0;
        intmod3VictTot = 0;
        intmod3Tx1Tot = 0;
        intmod3Tx3Tot = 0;
        cullStnCnt = 0;
    }

    /**
     * Writes the header information to the audit file.
     */
    private void writeAuditHeader() {
        afp.printf("Prop. channel (tx/rx): %f/%f    Grid (N/E): %d/%d    Time: %s\n",
                propTxFreq, propRxFreq, prop.getNorthGrid(), prop.getEastGrid(), sysTime);
        afp.println("============================================================================================\n\n");
    }

    /**
     * Updates the result object with summary statistics.
     *
     * @param result The result object to update
     */
    private void updateResultStatistics(EmcAnalysisResult result) {
        result.setCochanTot(cochanTot);
        result.setDesenTot(desenTot);
        result.setIntmod2VictTot(intmod2VictTot);
        result.setIntmod2Tx1Tot(intmod2Tx1Tot);
        result.setIntmod2Tx2Tot(intmod2Tx2Tot);
        result.setIntmod3VictTot(intmod3VictTot);
        result.setIntmod3Tx1Tot(intmod3Tx1Tot);
        result.setIntmod3Tx3Tot(intmod3Tx3Tot);
    }

    /**
     * Prints a summary of the EMC analysis.
     * Equivalent to the print_emc_summary function in the original C++ code.
     *
     * @throws IOException If an I/O error occurs
     */
    private void printEmcSummary() throws IOException {
        String sfname = summaryDir + "/" + String.format("%.5f", propTxFreq) + "." + hhmmss;
        Path summaryPath = Paths.get(sfname);
        Files.createDirectories(summaryPath.getParent());

        try (PrintWriter sfp = new PrintWriter(new FileWriter(sfname))) {
            // Write summary header
            writeSummaryHeader(sfp);

            // Write co-channel information
            sfp.println("** Co-channel Information Report");
            sfp.printf("   Number of Co-channel Stations : %d\n\n\n", cochanTot);

            // Write analysis results
            if (cullStnCnt == 0) {
                sfp.println("** No stations within culling distance for further analyses\f");
            } else {
                writeSummaryAnalysisResults(sfp);
            }
        }

        // Append summary to print file
        appendToFile(sfname, printFile);
    }

    /**
     * Writes the header section of the summary file.
     *
     * @param sfp The PrintWriter for the summary file
     */
    private void writeSummaryHeader(PrintWriter sfp) {
        sfp.printf("RUN DATE: %s%16s", sysDate, "");
        sfp.println("*****************************************************************                 PAGE   : 1");
        sfp.printf("RUN TIME: %s%16s", sysTime, "");
        sfp.println("*                                                               *                 PROGRAM: esemba0x");
        sfp.printf("USER ID : %-19s%5s", emcUid, "");
        sfp.println("*                  EMC ANALYSIS - SUMMARY LOG                   *");
        sfp.printf("%34s", "");
        sfp.println("*                 PROPOSED STATION INFORMATION                  *");
        sfp.printf("%34s", "");
        sfp.println("*                                                               *");
        sfp.printf("%34s", "");
        sfp.printf("*  PROP. SYSTEM    : %c%s%s-%s%32s*\n", prop.getSysCategory(),
                prop.getSysType(), prop.getSysNo(), prop.getSysSuffix(), "");
        sfp.printf("%34s", "");
        sfp.printf("*  RX FREQ (MHz)   : %10.4f   TX FREQ (MHz)   : %10.4f  *\n",
                propRxFreq, propTxFreq);
        sfp.printf("%34s", "");
        sfp.printf("*  GRID EAST       :      %5d   GRID NORTH      :      %5d  *\n",
                prop.getEastGrid(), prop.getNorthGrid());
        sfp.printf("%34s", "");
        sfp.printf("*  SUB-DISTRICT    :        %3s   ERP (DBW)       :     %6.2f  *\n",
                prop.getSubDistrict(), prop.getPwDbw());
        sfp.printf("%34s", "");
        sfp.printf("*  ANTENNA HT (M)  :        %3d   TERRAIN HT (M)  :      %5.1f  *\n",
                prop.getAntHeight(), prop.getHeightAsl());
    }

    /**
     * Writes the analysis results section of the summary file.
     *
     * @param sfp The PrintWriter for the summary file
     */
    private void writeSummaryAnalysisResults(PrintWriter sfp) {
        // Write desensitization information
        sfp.println("\n\n** Desensitization Information Report");
        sfp.printf("   Number of Desensitization Stations : %d\n\n\n", desenTot);

        // Write intermodulation information
        sfp.println("** Intermodulation Information Report");
        sfp.println("   Number of 2nd Order Intermodulation Stations :");
        sfp.printf("      Victim : %d\n", intmod2VictTot);
        sfp.printf("      TX1    : %d\n", intmod2Tx1Tot);
        sfp.printf("      TX2    : %d\n\n", intmod2Tx2Tot);

        sfp.println("   Number of 3rd Order Intermodulation Stations :");
        sfp.printf("      Victim : %d\n", intmod3VictTot);
        sfp.printf("      TX1    : %d\n", intmod3Tx1Tot);
        sfp.printf("      TX3    : %d\n", intmod3Tx3Tot);

        sfp.println("\f");
    }

    /**
     * Appends the content of one file to another.
     *
     * @param sourceFile The source file
     * @param targetFile The target file
     * @throws IOException If an I/O error occurs
     */
    private void appendToFile(String sourceFile, String targetFile) throws IOException {
        try (BufferedReader reader = new BufferedReader(new FileReader(sourceFile));
             PrintWriter writer = new PrintWriter(new FileWriter(targetFile, true))) {

            String line;
            while ((line = reader.readLine()) != null) {
                writer.println(line);
            }
        }
    }

    /**
     * Cleans up resources after the analysis is complete.
     *
     * @param interactive Whether this is an interactive analysis
     * @param interactiveMode The interactive mode
     * @param emcBatch The path to the EMC batch file
     */
    private void cleanUp(boolean interactive, String interactiveMode, String emcBatch) {
        // Close audit file
        if (afp != null) {
            afp.close();
        }

        // Delete batch file if this is an interactive analysis
        if (interactive && !"KEEP".equals(interactiveMode)) {
            try {
                Files.deleteIfExists(Paths.get(emcBatch));
            } catch (IOException e) {
                log.warn("Failed to delete batch file: {}", e.getMessage());
            }
        }
    }
}