package com.emc.dao.mapper;

import com.emc.model.MinUsableSignal;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Row mapper for MinUsableSignal entity.
 */
public class MinUsableSignalRowMapper implements RowMapper<MinUsableSignal> {
    
    @Override
    public MinUsableSignal mapRow(ResultSet rs, int rowNum) throws SQLException {
        MinUsableSignal minUsableSignal = new MinUsableSignal();
        minUsableSignal.setLowVhfSignal(rs.getFloat("LOW_VHF_SIGNAL"));
        minUsableSignal.setVhfSignal(rs.getFloat("VHF_SIGNAL"));
        minUsableSignal.setUhfSignal(rs.getFloat("UHF_SIGNAL"));
        minUsableSignal.setUhf800Signal(rs.getFloat("UHF_800_SIGNAL"));
        return minUsableSignal;
    }
}
