package com.emc.service;

/**
 * Service for intermodulation analysis.
 */
public interface IntermodulationService {
    
    /**
     * Performs 2-signal intermodulation analysis.
     * Equivalent to the intermod_2 function in the original C++ code.
     */
    void intermod2();
    
    /**
     * Performs 3-signal intermodulation analysis.
     * Equivalent to the intermod_3 function in the original C++ code.
     */
    void intermod3();
}
